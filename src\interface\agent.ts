import { OCRByImage } from './../api/exploration';
import type { IPage } from '.';
import type { ICallParams } from './exploration';
import type { IKnowledgeFilterItem } from './knowledge';

export interface IFetchAgentList extends IPage {
  name?: string;
  status?: string;
}

export interface IAgentItem {
  master_id: string;
  name: string;
  version: number;
  introduction: string;
  icon_url: string;
  config: IAgentItemConfig;
  status: string;
  deploy_id: string;
  service_name: string;
  service_url: string;
  model_name: string;
  publish_channel: string[];
  origins: [];
  model_category: string;
  id: string;
  creator_id: string;
  updater_id: string;
  created_at: string;
  updated_at: string;
  deleted_at: null;
}

export interface IAgentItemConfig {
  instruction: Role_prompt;
  knowledge_db: Knowledge_db;
  memory_config: Memory_config;
  opener_prompt: Opener_prompt;
  background_image: Background_image;
  generation_config: Generation_config;
  suggested_questions: Suggested_questions;
  followup_config: Followup_config;
}

export interface Role_prompt {
  value: string;
  is_enable: boolean;
}

export interface Knowledge_db {
  value: IKnowledgeFilterItem[];
  is_enable: boolean;
}
interface IUpdateKnowledge_db {
  value: string[];
  is_enable: boolean;
}
export interface IMemory_value {
  name: string;
  default_value: string;
  duration: string;
  description: string;
}
export interface Memory_config {
  value: IMemory_value[];
  is_enable: boolean;
}

export interface Opener_prompt {
  value: string;
  is_enable: boolean;
}

export interface Background_image {
  value: string;
  is_enable: boolean;
}

export interface Suggested_questions {
  value: string[];
  is_enable: boolean;
}

export interface IUpdateAgentProps {
  config?: IUpdateAgentConfig;
  icon_url?: string;
  introduction?: string;
  name?: string;
  deploy_id?: string;
}

export interface IUpdateAgentConfig {
  background_image?: Background_image;
  generation_config?: Generation_config;
  knowledge_db?: IUpdateKnowledge_db;
  memory_config?: Memory_config;
  opener_prompt?: Opener_prompt;
  instruction?: Role_prompt;
  suggested_questions?: Suggested_questions;
  followup_config?: Followup_config;
}

export interface Background_image {
  is_enable: boolean;
  value: string;
}

export interface Generation_config {
  is_enable: boolean;
  value: ICallParams;
}

export interface Opener_prompt {
  is_enable: boolean;
  value: string;
}

export interface Role_prompt {
  is_enable: boolean;
  value: string;
}

export interface Suggested_questions {
  is_enable: boolean;
  value: string[];
}

export interface IMessage {
  role: string;
  content: string;
  citations?: string[];
  image?: string;
  followup?: string[];
}

export interface Followup_config {
  is_enable: boolean;
  value: {
    svc_id: string;
    dialogue_rounds: number;
  };
}

export interface ILikeList {
  feedback_type: string;
  message_index: number;
  tags: string[];
  detailed_content: string;
}

export interface IPublishConfig {
  publish_channel: string[];
  origins: {
    website_name: string;
    domain: string;
  }[];
}
